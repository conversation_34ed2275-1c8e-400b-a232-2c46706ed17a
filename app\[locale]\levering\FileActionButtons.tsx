"use client";

import React, { useState } from "react";
import { FaDownload, FaTrashAlt } from "react-icons/fa";
import ConfirmDeleteModal from "./ConfirmModal";
import { useUploadedFiles } from "./UploadedFilesContext";
import { IDeliveredFile } from "@/app/interfaces/IUploadFile";
import { Button } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";

interface FileActionButtonProps {
  file: IDeliveredFile;
  deleteLabel: string;
  downLoadLabel: string;
  confirmDeleteHeading: string;
  confirmDeleteMessage: string;
  confirmDeleteConfirmBtn: string;
  cancelLabel: string;
}

const FileActionButtons: React.FC<FileActionButtonProps> = ({
  file,
  deleteLabel,
  downLoadLabel,
  cancelLabel,
  confirmDeleteConfirmBtn,
  confirmDeleteHeading,
  confirmDeleteMessage,
}) => {
  const { handleDownloadBlob, handleDeleteFile, isDownloadComplete } =
    useUploadedFiles();

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);

  const handleDeleteClick = () => {
    if (!file.isRejected) {
      setIsDeleteModalOpen(true);
    } else {
      handleDeleteFile(file);
    }
  };

  const handleConfirmDelete = async () => {
    setIsDeleteModalOpen(false);
    await handleDeleteFile(file);
  };

  return (
    <>
      <div className="flex flex-col sm:flex-row items-center gap-3">
        <Button
          type="button"
          variant="outline"
          disabled={
            file.isRejected ||
            file.downloading ||
            !file.uploadFinished ||
            file.deleting ||
            !isDownloadComplete
          }
          onClick={async () => {
            await handleDownloadBlob(file.fileGuid, file.fileName, file.testPartId);
          }}
          className="p-3 items-center disabled:text-[#575757] normal-case whitespace-nowrap w-full sm:w-24"
        >
          {file.downloading ? (
            <Spinner size="sm" />
          ) : (
            <div className="flex items-center gap-2">
              <FaDownload role="img" aria-label="download-icon" />
              {downLoadLabel}
            </div>
          )}
        </Button>

        <Button
          variant="ghost"
          disabled={
            !file.uploadFinished ||
            file.downloading ||
            file.deleting ||
            !isDownloadComplete
          }
          onClick={handleDeleteClick}
          className="p-3 items-center normal-case whitespace-nowrap w-full sm:w-20"
        >
          {file.deleting ? (
            <Spinner size="sm" />
          ) : (
            <div className="flex items-center gap-2">
              <FaTrashAlt role="img" aria-label="delete-icon" />
              {deleteLabel}
            </div>
          )}
        </Button>
      </div>

      <ConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        fileName={file.fileName}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleConfirmDelete}
        confirmDeleteHeading={confirmDeleteHeading}
        confirmDeleteMessage={confirmDeleteMessage}
        confirmDeleteConfirmBtnLabel={confirmDeleteConfirmBtn}
        cancelLabel={cancelLabel}
      />
    </>
  );
};

export default FileActionButtons;
