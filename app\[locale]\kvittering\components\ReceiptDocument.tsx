import React from "react";
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Image,
} from "@react-pdf/renderer";
import UdirLogoImg from "@/app/assets/udir-logo.png";

// Function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
};

// Create styles
const styles = StyleSheet.create({
  page: {
    flexDirection: "column",
    backgroundColor: "#FFFFFF",
    padding: 30,
  },
  section: {
    margin: 10,
    padding: 10,
    flexGrow: 1,
  },
  title: {
    fontSize: 24,
    textAlign: "center",
    marginBottom: 20,
  },
  card: {
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    backgroundColor: "#F5F5F5",
  },
  cardText: {
    fontSize: 12,
    marginBottom: 5,
  },
  fileListTitle: {
    fontSize: 16,
    marginTop: 20,
    marginBottom: 10,
  },
  table: {
    width: "auto",
    marginBottom: 10,
  },
  tableRow: {
    margin: "auto",
    flexDirection: "row",
  },
  tableColHeader: {
    width: "33%",
    borderStyle: "solid",
    borderColor: "#bfbfbf",
    borderBottomColor: "#000",
    borderWidth: 1,
    backgroundColor: "#f0f0f0",
    fontSize: 10,
    padding: 5,
  },
  tableCol: {
    width: "33%",
    borderStyle: "solid",
    borderColor: "#bfbfbf",
    borderWidth: 1,
    borderTopWidth: 0,
    fontSize: 10,
    padding: 5,
  },
});

interface ReceiptDocumentProps {
  receiptInfo: any; // Replace 'any' with actual type for receipt info
  submittedFiles: any; // Replace 'any' with actual type for submitted files
}

const ReceiptDocument: React.FC<ReceiptDocumentProps> = ({
  receiptInfo,
  submittedFiles,
}) => (
  <Document>
    <Page size="A4" style={styles.page}>
      <View style={{ alignItems: "center", marginBottom: 10 }}>
        <Image
          src={UdirLogoImg}
          style={{ width: 120, height: 40, marginBottom: 10 }}
        />
      </View>
      <View style={styles.section}>
        <Text style={styles.title}>Kvittering for levert eksamen</Text>

        <View style={styles.card}>
          <Text style={styles.cardText}>
            Kandidatnummer: {receiptInfo?.candidateNumber}
          </Text>
          <Text style={styles.cardText}>
            Fagkode: {receiptInfo?.subjectCode}
          </Text>
          <Text style={styles.cardText}>
            Levert dato: {receiptInfo?.deliveryDate}
          </Text>
          <Text style={styles.cardText}>
            Levert klokkeslett: {receiptInfo?.deliveryTime}
          </Text>
        </View>

        <Text style={styles.fileListTitle}>
          Antall filer levert: {submittedFiles?.length}
        </Text>
        <View style={styles.table}>
          <View style={styles.tableRow}>
            <Text style={styles.tableColHeader}>Filnavn</Text>
            <Text style={styles.tableColHeader}>Størrelse</Text>
            <Text style={styles.tableColHeader}>Opplastingstid</Text>
          </View>
          {submittedFiles?.map((file: any, index: number) => (
            <View style={styles.tableRow} key={index}>
              <Text style={styles.tableCol}>{file.fileName}</Text>
              <Text style={styles.tableCol}>{formatFileSize(file.size)}</Text>
              <Text style={styles.tableCol}>
                kl{" "}
                {new Date(file.uploadDate).toLocaleTimeString("nb-NO", {
                  hour: "2-digit",
                  minute: "2-digit",
                })}
              </Text>
            </View>
          ))}
        </View>
      </View>
    </Page>
  </Document>
);

export default ReceiptDocument;
