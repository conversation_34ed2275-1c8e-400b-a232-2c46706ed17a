"use client";

import { LocalizationMap, Viewer, Worker } from "@react-pdf-viewer/core";
import { defaultLayoutPlugin } from "@react-pdf-viewer/default-layout";
import { zoomPlugin } from "@react-pdf-viewer/zoom";
import { useState } from "react";
import { getBlobForPreview } from "@/app/lib/getBlob";
import { FaEye } from "react-icons/fa";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { logEvent } from "@/app/lib/appInsightsClient";
import { IoClose } from "react-icons/io5";
import { logActivity } from "@/app/lib/logActivity";
import { OperationEnum } from "@/app/enums/OperationEnum";
import { getTestPart } from "@/app/lib/getTestPart";

// Import CSS
import "@react-pdf-viewer/core/lib/styles/index.css";
import "@react-pdf-viewer/default-layout/lib/styles/index.css";

import nb_NO from "./locales/nb_NO.json";

type Scale = "0.75" | "1.0" | "1.25" | "1.5";

interface iPreviewBtnProps {
  previewTxt: string;
  genFileName: string;
  orgFileName: string;
  fileExtension: string;
  previewHoverLabel: string;
  errorLoadingExerciseLabel: string;
  subjectCode: string;
  testPartId?: number;
  preparation?: boolean;
}

export default function PreviewBtn({
  previewTxt,
  genFileName,
  orgFileName,
  fileExtension,
  previewHoverLabel,
  errorLoadingExerciseLabel,
  subjectCode,
  testPartId,
  preparation,
}: iPreviewBtnProps) {
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState<boolean>(false);
  const [blobUrl, setBlobUrl] = useState<string | undefined>("");
  const [currentScale, setCurrentScale] = useState<Scale>("1.25");
  const { toast } = useToast();

  const zoomPluginInstance = zoomPlugin();
  const { zoomTo } = zoomPluginInstance;

  // Create default layout plugin with custom minimal toolbar and no sidebar
  const defaultLayoutPluginInstance = defaultLayoutPlugin({
    sidebarTabs: () => [], // Remove all sidebar tabs (bookmarks, thumbnails, etc.)
    renderToolbar: (Toolbar: any) => (
      <Toolbar>
        {(slots: any) => {
          const {
            CurrentPageInput,
            GoToFirstPage,
            GoToLastPage,
            GoToNextPage,
            GoToPreviousPage,
            NumberOfPages,
          } = slots;
          return (
            <div
              style={{
                alignItems: "center",
                display: "flex",
                width: "100%",
                padding: "8px 16px",
                backgroundColor: "#f8f9fa",
                borderBottom: "1px solid #dee2e6",
                justifyContent: "space-between",
              }}
            >
              {/* Page navigation */}
              <div
                style={{
                  alignItems: "center",
                  display: "flex",
                  gap: "8px",
                }}
              >
                <GoToFirstPage />
                <GoToPreviousPage />
                <div
                  style={{ display: "flex", alignItems: "center", gap: "4px" }}
                >
                  <CurrentPageInput />
                  <span>av</span>
                  <NumberOfPages />
                </div>
                <GoToNextPage />
                <GoToLastPage />
              </div>

              {/* Custom zoom controls */}
              <div
                style={{
                  alignItems: "center",
                  display: "flex",
                  gap: "8px",
                }}
              >
                <select
                  className="border rounded px-2 py-1 text-sm"
                  value={currentScale}
                  onChange={(e) => {
                    const newScale = e.target.value as Scale;
                    setCurrentScale(newScale);
                    zoomTo(parseFloat(newScale));
                  }}
                >
                  <option value="0.75">75%</option>
                  <option value="1.0">100%</option>
                  <option value="1.25">125%</option>
                  <option value="1.5">150%</option>
                </select>
              </div>
            </div>
          );
        }}
      </Toolbar>
    ),
  });

  async function previewPDF() {
    try {
      const blobUrl = await getBlobForPreview(
        genFileName,
        orgFileName,
        "oppgaver"
      );

      if (blobUrl) {
        await logEvent("examFilePreview", {
          GenFileName: genFileName,
          FileName: orgFileName,
          SubjectCode: subjectCode,
        });

        await logActivity(
          genFileName,
          getTestPart(testPartId ?? 0),
          preparation
            ? OperationEnum.ForhaandvisPreparation
            : OperationEnum.Forhaandvistoppgave
        );

        setBlobUrl(blobUrl);
      } else {
        toast({
          variant: "destructive",
          title: errorLoadingExerciseLabel,
        });
      }
    } catch (error) {
      console.error("Error previewing PDF:", error);
    }
  }

  function openPreviewModal() {
    previewPDF();
    setIsPreviewModalOpen(true);
  }

  function closePreviewModal() {
    setBlobUrl("");
    setIsPreviewModalOpen(false);
  }

  return (
    <>
      <div
        className={fileExtension !== ".pdf" ? "tooltip" : ""}
        data-tip={previewHoverLabel}
      />
      {blobUrl && (
        <Dialog open={isPreviewModalOpen} onOpenChange={closePreviewModal}>
          <DialogContent className="[&>button]:hidden sm:max-w-[900px] max-w-[800px] bg-white max-h-[90vh] flex flex-col">
            <DialogTitle className="flex place-content-between sticky top-0 bg-white z-10 py-4 border-none border-b">
              {genFileName}
              <IoClose
                className="text-lg cursor-pointer"
                onClick={closePreviewModal}
              />
            </DialogTitle>
            <DialogDescription className="sr-only">
              Forhåndsvisning av PDF-dokument: {orgFileName}
            </DialogDescription>
            <div
              className="flex-1"
              style={{
                height: "70vh",
                overflowY: "auto",
                overflowX: "hidden",
                position: "relative",
              }}
            >
              <Worker workerUrl="/pdf.worker.min.js">
                <Viewer
                  fileUrl={blobUrl}
                  plugins={[defaultLayoutPluginInstance, zoomPluginInstance]}
                  localization={nb_NO as any as LocalizationMap}
                />
              </Worker>
            </div>
          </DialogContent>
        </Dialog>
      )}
      <button onClick={openPreviewModal}>
        <span className="btn btn-ghost normal-case flex gap-2 h-12 font-semibold items-center hover:bg-base-100 hover:neutral-foreground rounded-[3px] sm:w-52 disabled:text-[#575757]">
          <FaEye className="h-4 w-4" role="img" aria-label="Forhåndsvis ikon" />
          {previewTxt}
        </span>
      </button>
    </>
  );
}
