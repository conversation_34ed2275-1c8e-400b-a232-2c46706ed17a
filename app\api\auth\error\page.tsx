"use client";

import { getSession, signOut } from "next-auth/react";
import <PERSON><PERSON> from "js-cookie";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { logActivity } from "@/app/lib/logActivity";
import { OperationEnum } from "@/app/enums/OperationEnum";

const PGS_COOKIE_NAME = process.env.PGS_USER_COOKIE_NAME || "";

const styles = {
  container: {
    fontFamily: "Arial, Helvetica, sans-serif",
    margin: "80px",
  },
  title: {
    fontSize: "3.5rem",
  },
  content: {
    fontSize: "1.2rem",
  },

  button: {
    display: "inline-block",
    padding: "0.5rem 1rem",
    fontSize: "1rem",
    fontWeight: 500,
    color: "white",
    backgroundColor: "#000000",
    borderRadius: "0.375rem",
    boxShadow: "0 2px 5px rgba(0, 0, 0, 0.1)",
    border: "none",
    cursor: "pointer",
    transition: "background-color 0.2s ease, box-shadow 0.2s ease",
  },

  buttonHover: {
    backgroundColor: "#333333", // Darker gray on hover
    boxShadow: "0 4px 10px rgba(0, 0, 0, 0.2)", // Deeper shadow
  },
};

const clearSessionAndRedirect = async (url: string) => {
  Cookie.remove("next-auth.csrf-token");
  Cookie.remove(PGS_COOKIE_NAME);
  sessionStorage.clear();
  await signOut({ redirect: false });
  window.location.href = url;
};

const handleLogout = async () => {
  const session: any = await getSession();

  if (!session) {
    const hostname = window.location.hostname;
    const match = hostname.match(/pgs-(dev|qa|test)\.udir\.no/);
    const env = match
      ? match[1]
      : hostname === "pgs.udir.no"
      ? "prod"
      : hostname.includes("localhost")
      ? "dev"
      : "default";

    const redirectUrl =
      env === "prod"
        ? "https://uidp.udir.no/connect/endsession"
        : env === "test"
        ? "https://uidp-tst.udir.no/connect/endsession"
        : `https://uidp-${env}.udir.no/connect/endsession`;

    await logActivity("", "", OperationEnum.LoggetUt);

    await clearSessionAndRedirect(redirectUrl);

    return;
  }

  const queryParams = {
    idtoken: session?.user?.idToken,
  };
  const queryString = new URLSearchParams(queryParams).toString();
  const response = await fetch(
    `${window.location.origin}/api/federatedlogout?${queryString}`,
    { cache: "no-store" }
  );
  const data = await response.json();
  if (response.ok) {
    await clearSessionAndRedirect(data.url);
  }
};

const Error = ({}) => {
  const [isHovered, setIsHovered] = useState(false);

  const environment =
    process.env.PGS_URL?.match(/https:\/\/pgs-(\w+).udir.no/)?.[1] ?? "prod";

  let kandidatUrl = "";

  switch (environment) {
    case "dev":
      kandidatUrl = "https://kandidat-dev.udir.no";
      break;
    case "test":
      kandidatUrl = "https://kandidat-tst.udir.no";
      break;
    case "qa":
      kandidatUrl = "https://kandidat-qa.udir.no";
      break;
    default:
      kandidatUrl = "https://kandidat.udir.no";
      break;
  }

  const errorTitle = "Ingen tilgang";

  return (
    <div style={styles.container}>
      <h1 style={styles.title}>{errorTitle}</h1>
      <div style={styles.content}>
        <p>
          Du har ikke tilgang til PGS. Dersom du skal gjennomføre eksamen, så gå
          til <a href={kandidatUrl}>kandidat.udir.no</a>
        </p>

        <Button
          style={
            isHovered
              ? { ...styles.button, ...styles.buttonHover }
              : styles.button
          }
          onMouseOver={() => setIsHovered(true)}
          onMouseOut={() => setIsHovered(false)}
          variant="secondary"
          onClick={handleLogout}
        >
          Logg ut
        </Button>
      </div>
    </div>
  );
};

export default Error;
