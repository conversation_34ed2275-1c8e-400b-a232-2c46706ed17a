"use client";

import {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
  ReactNode,
  useCallback,
} from "react";
import { useSession } from "next-auth/react";
import { HubConnection, HubConnectionState } from "@microsoft/signalr";
import { getSignalRConnection } from "../lib/signalRService";

interface SignalRContextValue {
  connection: HubConnection | undefined;
  connectionState: ConnectionState;
  lastError: Error | null;
  reconnect: () => Promise<void>;
}

type ConnectionState =
  | "CONNECTING"
  | "CONNECTED"
  | "DISCONNECTED"
  | "FAILED"
  | "RECONNECTING";

const SignalRContext = createContext<SignalRContextValue>({
  connection: undefined,
  connectionState: "DISCONNECTED",
  lastError: null,
  reconnect: async () => {},
});

export const SignalRProvider = ({ children }: { children: ReactNode }) => {
  const [connection, setConnection] = useState<HubConnection | undefined>();
  const [connectionState, setConnectionState] =
    useState<ConnectionState>("DISCONNECTED");
  const [lastError, setLastError] = useState<Error | null>(null);
  const retryCount = useRef(0);
  const maxRetries = 5;
  const abortController = useRef(new AbortController());
  const isMounted = useRef(true);
  const connectionRef = useRef<HubConnection | undefined>(undefined);

  const setupConnectionHandlersRef = useRef<(conn: HubConnection) => void | undefined>(undefined);
  const startConnectionRef = useRef<(conn: HubConnection) => Promise<void> | undefined>(undefined);
  const initializeConnectionRef = useRef<(() => Promise<void>) | undefined>(undefined);
  const attemptReconnectRef = useRef<(() => Promise<void>) | undefined>(undefined);
  const handleReconnectRef = useRef<(() => Promise<void>) | undefined>(undefined);

  const cleanupConnection = useCallback(async () => {
    const conn = connectionRef.current;
    if (!conn) return;

    try {
      // Remove all listeners to prevent memory leaks
      conn.off("close");
      conn.off("reconnected");
      conn.off("reconnecting");
      conn.off("Connected");

      if (conn.state !== HubConnectionState.Disconnected) {
        await conn.stop();
      }
    } catch (error) {
      console.error("Error during connection cleanup:", error);
    }
  }, []);

  const setupConnectionHandlers = useCallback((conn: HubConnection) => {
    // Remove any existing handlers first
    conn.off("close");
    conn.off("reconnected");
    conn.off("reconnecting");

    conn.onclose(async (error) => {
      console.log("Connection closed:", error);
      if (isMounted.current) {
        setConnectionState("DISCONNECTED");
        await attemptReconnectRef.current?.();
      }
    });

    conn.onreconnecting((error) => {
      console.log("Connection reconnecting:", error);
      if (isMounted.current) {
        setConnectionState("RECONNECTING");
        setLastError(error as Error);
      }
    });

    conn.onreconnected(() => {
      console.log("Connection reestablished");
      if (isMounted.current) {
        setConnectionState("CONNECTED");
        setLastError(null);
        retryCount.current = 0;
      }
    });

    conn.on("Connected", () => {
      console.log("Connection established");
      if (isMounted.current) {
        setConnectionState("CONNECTED");
        setLastError(null);
        retryCount.current = 0;
      }
    });
  }, []);

  // Set the ref after the function is defined
  useEffect(() => {
    setupConnectionHandlersRef.current = setupConnectionHandlers;
  }, [setupConnectionHandlers]);

  const startConnection = useCallback(async (conn: HubConnection) => {
    try {
      if (conn.state === HubConnectionState.Disconnected) {
        setConnectionState("CONNECTING");
        await conn.start();
      }

      if (conn.state === HubConnectionState.Connected) {
        setConnectionState("CONNECTED");
      }
    } catch (error) {
      console.error("Failed to start connection:", error);
      if (isMounted.current) {
        setConnectionState("FAILED");
        setLastError(error as Error);
      }
      throw error;
    }
  }, []);

  // Set the ref after the function is defined
  useEffect(() => {
    startConnectionRef.current = startConnection;
  }, [startConnection]);

  const initializeConnection = useCallback(async (): Promise<void> => {
    if (!isMounted.current) return;

    try {
      // Cancel any ongoing connection attempts
      abortController.current.abort();
      abortController.current = new AbortController();

      // Clean up existing connection
      await cleanupConnection();

      const conn = await getSignalRConnection();
      setupConnectionHandlers(conn);
      connectionRef.current = conn;
      setConnection(conn);
      await startConnection(conn);
    } catch (error) {
      console.error("Connection attempt failed:", error);
      if (isMounted.current) {
        setConnectionState("FAILED");
        setLastError(error as Error);
      }
    }
  }, [cleanupConnection, setupConnectionHandlers, startConnection]);

  // Set the ref after the function is defined
  useEffect(() => {
    initializeConnectionRef.current = initializeConnection;
  }, [initializeConnection]);

  const attemptReconnect = useCallback(async () => {
    attemptReconnectRef.current = async () => {
      if (
        !isMounted.current ||
        connectionState === "CONNECTING" ||
        connectionState === "RECONNECTING"
      ) {
        return;
      }

      if (retryCount.current >= maxRetries) {
        console.log("Max retries reached, giving up");
        if (isMounted.current) {
          setConnectionState("FAILED");
        }
        return;
      }

      retryCount.current += 1;
      const delay = Math.min(1000 * Math.pow(2, retryCount.current), 30000);

      console.log(`Attempting reconnect #${retryCount.current} in ${delay}ms`);

      const timer = setTimeout(async () => {
        try {
          if (isMounted.current) {
            setConnectionState("RECONNECTING");
          }
          await initializeConnectionRef.current?.();
        } catch (error) {
          console.error("Reconnect failed:", error);
          if (isMounted.current) {
            setConnectionState("FAILED");
            setLastError(error as Error);
          }
          await attemptReconnectRef.current?.();
        }
      }, delay);

      abortController.current.signal.addEventListener("abort", () => {
        clearTimeout(timer);
      });
    };
    attemptReconnectRef.current();
  }, [connectionState]);

  const handleReconnect = useCallback(async () => {
    handleReconnectRef.current = async () => {
      if (!isMounted.current) return;

      retryCount.current = 0;
      await initializeConnectionRef.current?.();
    };
    handleReconnectRef.current();
  }, []);

  // Handle network status changes
  useEffect(() => {
    const handleOnline = () => {
      if (
        isMounted.current &&
        (connectionState === "FAILED" || connectionState === "DISCONNECTED")
      ) {
        handleReconnect();
      }
    };

    window.addEventListener("online", handleOnline);
    return () => window.removeEventListener("online", handleOnline);
  }, [connectionState, handleReconnect]);

  // Initial connection setup, only when authenticated
  const { status } = useSession();

  useEffect(() => {
    isMounted.current = true;
    if (status === "authenticated") {
      initializeConnection();
    }
    return () => {
      isMounted.current = false;
      abortController.current.abort();
      cleanupConnection();
    };
  }, [initializeConnection, cleanupConnection, status]);

  return (
    <SignalRContext.Provider
      value={{
        connection,
        connectionState,
        lastError,
        reconnect: handleReconnectRef.current || (async () => {}),
      }}
    >
      {children}
    </SignalRContext.Provider>
  );
};

export const useSignalR = () => {
  const context = useContext(SignalRContext);
  if (!context) {
    throw new Error("useSignalR must be used within a SignalRProvider");
  }
  return context;
};
