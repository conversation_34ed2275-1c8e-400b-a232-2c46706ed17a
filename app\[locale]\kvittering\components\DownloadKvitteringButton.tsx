"use client";
import { PDFDownloadLink } from "@react-pdf/renderer";
import ReceiptDocument from "./ReceiptDocument";

interface DownloadKvitteringButtonProps {
  receiptInfo: any;
  submittedFiles: any;
  downloadRecieptLabel: string;
}

export default function DownloadKvitteringButton({ receiptInfo, submittedFiles, downloadRecieptLabel }: DownloadKvitteringButtonProps) {
  return (
    <PDFDownloadLink
      document={<ReceiptDocument receiptInfo={receiptInfo} submittedFiles={submittedFiles} />}
      fileName="kvittering.pdf"
      style={{ textDecoration: "none", color: "#2563eb", fontWeight: 500 }}
    >
      {({ loading }) => loading ? "Laster..." : downloadRecieptLabel}
    </PDFDownloadLink>
  );
}
