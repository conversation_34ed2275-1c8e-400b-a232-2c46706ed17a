"use client";

import { useEffect } from "react";
import { validatePassword } from "@/app/lib/validatePassword";
import { useActionState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useState } from "react";
import { RouteEnum } from "@/app/enums/RouteEnum";
import { error } from "console";
import { useFormState, useFormStatus } from "react-dom";

interface IDayPasswordProps {
  sendLabel: string;
  writePasswordLinkLabel: string;
  writeDayPasswordLabel: string;
  fieldEmptyLabel: string;
  wrongPasswordLabel: string;
}
export default function DayPassword({
  sendLabel,
  writePasswordLinkLabel,
  writeDayPasswordLabel,
  wrongPasswordLabel,
  fieldEmptyLabel,
}: IDayPasswordProps) {
  const initialState = {
    validated: false,
    errorMessage: "",
  };

  const [dayCodeInputHidden, setDayCodeInputHidden] = useState(true);
  const [initialLoad, setInitialLoad] = useState(true);
  const [state, formAction] = useActionState(validatePassword, initialState);
  const [firstRender, setFirstRender] = useState(true);
  const [showError, setShowError] = useState(false);
  const router = useRouter();
  /* const errorMessage =
    state.errorMessage === "fieldEmpty" ? fieldEmptyLabel : wrongPasswordLabel;*/

  useEffect(() => {
    if (!firstRender) {
      if (state.validated) {
        //router.push(RouteEnum.Klar);
        window.location.reload();
      } else {
        setShowError(true);
      }
    } else {
      setFirstRender(false);
    }
    setInitialLoad(false);
  }, [state, firstRender]);

  function SubmitBtn() {
    const { pending } = useFormStatus();
    return (
      <button
        type="submit"
        className="btn btn-neutral normal-case w-full sm:w-20 border-0 text-white hover:bg-primary"
        disabled={pending}
      >
        {pending ? (
          <span className="loading loading-spinner loading-sm"></span>
        ) : (
          <span>{sendLabel}</span>
        )}
      </button>
    );
  }

  return (
    <>
      <div className="mt-5 text-end" hidden={!dayCodeInputHidden}>
        <a
          href="#"
          className="text-base link hover:text-primary"
          onClick={() => setDayCodeInputHidden(false)}
        >
          {writePasswordLinkLabel}
        </a>
      </div>
      <div
        className={`mt-8 transition-opacity duration-500 ${
          dayCodeInputHidden ? "opacity-0" : "opacity-100"
        }`}
      >
        <div className="card rounded-none w-full bg-white">
          <div className="card-body">
            <label htmlFor="password" className="font-medium">
              {writeDayPasswordLabel}
            </label>
            <form
              action={formAction}
              className="flex flex-col sm:flex-row gap-4"
            >
              <input
                type="password"
                id="password"
                name="password"
                autoComplete="off"
                className={
                  state.errorMessage
                    ? "input-error input-bordered input w-full bg-gray-100 input-md rounded-t"
                    : "border-b-2 rounded-t input-md border-black w-full bg-gray-100"
                }
                placeholder="Dagspassord"
              />
              <SubmitBtn />
            </form>
            {showError && (
              <label className="text-error">{state.errorMessage}</label>
            )}{" "}
          </div>
        </div>
      </div>
    </>
  );
}
