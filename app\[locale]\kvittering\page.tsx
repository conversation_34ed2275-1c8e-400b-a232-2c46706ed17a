import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import ReceiptInfo from "./components/ReceiptInfo";
import SubmittedFiles from "./components/SubmittedFiles";
import DownloadReceipt from "./components/DownloadReceipt";
import { getStatusInfoFromPgsa } from "@/app/lib/getStatusInfoFromPgsa";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { getDeliveredFiles } from "@/app/lib/getDeliveredFiles";
import { CandidateStatusEnum } from "@/app/enums/CandidateStatusEnum";
import { IDeliveredFile } from "@/app/interfaces/IUploadFile";

export const metadata: Metadata = {
  title: "PGS - Kvittering",
  description: "Last ned kvittering",
};

const Kvittering = async ({}) => {
  const t = await getTranslations("Kvittering");
  const userSessionData = await getUserSessionData();

  const statusInfo = await getStatusInfoFromPgsa(userSessionData.userId);

  let deliveredExams: IDeliveredFile[] = [];
  if (statusInfo && statusInfo.Status === CandidateStatusEnum.Levert) {
    try {
      console.log("Getting delivered files...", userSessionData.candidateNumber, userSessionData.candidateGroupCode);
      deliveredExams = await getDeliveredFiles(
        userSessionData.candidateNumber,
        userSessionData.candidateGroupCode,
        true
      );
    } catch (error) {
      console.error("Error getting delivered files:", error);
    }
  }

  const receiptInfo = {
    candidateNumber: userSessionData.candidateNumber,
    subjectCode: statusInfo?.SubjectCode,
    deliveryDate: new Date().toLocaleDateString("nb-NO"),
    deliveryTime: new Date().toLocaleTimeString("nb-NO"),
  };

  return (
    <>
      <div className="flex flex-col gap-6 mb-8">
        <h1 className="text-5xl font-bold">{t("ReceiptDeliveredExam")}</h1>
      </div>
      <div className="flex flex-col w-full gap-8">
        <div key="receiptInfo">
          <ReceiptInfo statusInfo={statusInfo} userSessionData={userSessionData} />
        </div>
        <div key="submittedFiles">
          <SubmittedFiles statusInfo={statusInfo} userSessionData={userSessionData} deliveredExams={deliveredExams} />
        </div>
        <div key="downloadReciept">
          <DownloadReceipt receiptInfo={receiptInfo} submittedFiles={deliveredExams} />
        </div>
      </div>
    </>
  );
};

export default Kvittering;
