import { getTranslations } from "next-intl/server";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { StatusInfoEmptyError } from "@/app/lib/exceptionTypes";
import { getStatusInfoFromPgsa } from "@/app/lib/getStatusInfoFromPgsa";
import { RouteEnum } from "@/app/enums/RouteEnum";
import Link from "next/link";
import { FaArrowRight } from "react-icons/fa";

const ExamInfo = async () => {
  const userSessionData = await getUserSessionData();

  const [statusInfo, t] = await Promise.all([
    getStatusInfoFromPgsa(userSessionData.userId),
    getTranslations("Eksamensoppgave"),
  ]);

  if (!statusInfo) throw new StatusInfoEmptyError();

  return (
    <div className="w-full bg-white rounded-none border border-gray-200 shadow-sm">
      <div className="p-6">
        <h2 className="font-normal text-base text-gray-900 mb-4">
          {t("DeliverExamIn")} {statusInfo.SubjectName}
        </h2>
        <div className="flex gap-2 items-center mt-2">
          <span className="flex w-4 h-4 bg-secondary rounded-full" />
          <p>{t("ExamOpenToDeliver")}</p>
        </div>
        <div className="flex flex-col mt-4 gap-4">
          <Link
            href={RouteEnum.Levering}
            className="inline-flex items-center justify-center whitespace-nowrap rounded-[3px] text-sm font-medium ring-offset-background transition-colors focus-visible:outline focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-neutral text-white hover:bg-primary normal-case border-0 h-10 px-4 py-2 w-full sm:w-48"
          >
            <span className="flex items-center gap-2">
              {t("DeliverExam")}
              <FaArrowRight role="img" aria-label="Pil høyre" />
            </span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ExamInfo;
