"use client";

import React from "react";
import { FaBars } from "react-icons/fa";
import { IoLanguageOutline, IoLogOutOutline } from "react-icons/io5";
import Image from "next/image";
import { createSharedPathnamesNavigation } from "next-intl/navigation";
import { locales } from "@/middleware";
import { federatedlogout } from "@/app/lib/federatedLogout";
import UdirLogoImg from "@/app/assets/udir-logo.png";

const { useRouter, usePathname } = createSharedPathnamesNavigation({ locales });

function NavBar({ ChangeLanguage, Logout, locale, language }: any) {
  const router = useRouter();
  const pathname = usePathname();

  const handleLanguageChange = async () => {
    const newLocale = locale === "nb" ? "nn" : "nb";

    // Set the new locale in cookie
    await fetch(`/api/setDefaultLanguage?defaultLocale=${newLocale}`);

    // Navigate to the new locale
    router.replace(pathname, { locale: newLocale });

    // Refresh data from server and re-render server components
    router.refresh();
  };

  return (
    <div className="navbar bg-base-100">
      <div className="navbar-start">
        <Image src={UdirLogoImg} alt="Udir logo" width={200} height={150} />
      </div>

      <div className="navbar-end">
        <div className="hidden md:flex items-center gap-4">
          <button onClick={handleLanguageChange}>
            <div className="flex gap-1 items-center hover:text-primary cursor-pointer">
              <span className="text-lg">
                <IoLanguageOutline role="img" aria-label={ChangeLanguage} />
              </span>
              <span>{ChangeLanguage}</span>
            </div>
          </button>
          <button
            className="btn btn-outline normal-case text-base"
            onClick={async () => {
              sessionStorage.removeItem("pgs-locale-set");
              sessionStorage.removeItem("pgs-authenticated");
              sessionStorage.removeItem("rejectedFiles");

              await federatedlogout();
            }}
          >
            <span className="text-xl">
              <IoLogOutOutline role="img" aria-label={Logout} />
            </span>
            {Logout}
          </button>
        </div>
        <div className="flex md:hidden">
          <details className="dropdown dropdown-end">
            <summary className="m-1 btn btn-ghost text-xl">
              <FaBars role="img" aria-label="Meny ikon" />
            </summary>
            <ul className="p-2 shadow menu dropdown-content z-[1] rounded w-52 bg-stone-100">
              <li>
                <button onClick={handleLanguageChange}>
                  <div className="flex gap-3 items-center cursor-pointer">
                    <span className="text-lg">
                      <IoLanguageOutline
                        role="img"
                        aria-label={ChangeLanguage}
                      />
                    </span>
                    <span>{ChangeLanguage}</span>
                  </div>
                </button>
              </li>
              <li>
                <button
                  className="flex gap-3 items-center"
                  onClick={async () => {
                    sessionStorage.removeItem("pgs-locale-set");
                    sessionStorage.removeItem("pgs-authenticated");
                    sessionStorage.removeItem("rejectedFiles");

                    await federatedlogout();
                  }}
                >
                  <span className="text-xl">
                    <IoLogOutOutline role="img" aria-label={Logout} />
                  </span>
                  <span>Logg ut</span>
                </button>
              </li>
            </ul>
          </details>
        </div>
      </div>
    </div>
  );
}

export default NavBar;
