"use client";

import React from "react";
import { FaBars } from "react-icons/fa";
import { IoLanguageOutline, IoLogOutOutline } from "react-icons/io5";
import Image from "next/image";
import { createSharedPathnamesNavigation } from "next-intl/navigation";
import { locales } from "@/middleware";
import { federatedlogout } from "@/app/lib/federatedLogout";
import UdirLogoImg from "@/app/assets/udir-logo.png";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const { useRouter, usePathname } = createSharedPathnamesNavigation({ locales });

function NavBar({ ChangeLanguage, Logout, locale, language }: any) {
  const router = useRouter();
  const pathname = usePathname();

  const handleLanguageChange = async () => {
    const newLocale = locale === "nb" ? "nn" : "nb";
    
    // Set the new locale in cookie
    await fetch(`/api/setDefaultLanguage?defaultLocale=${newLocale}`);
    
    // Navigate to the new locale
    router.replace(pathname, { locale: newLocale });

    // Refresh data from server and re-render server components
    router.refresh();
  };

  const handleLogout = async () => {
    sessionStorage.removeItem("pgs-locale-set");
    sessionStorage.removeItem("pgs-authenticated");
    sessionStorage.removeItem("rejectedFiles");
    await federatedlogout();
  };

  return (
    <div className="flex items-center justify-between w-full px-4 py-2 bg-base-100">
      <div className="flex-shrink-0">
        <Image
          src={UdirLogoImg}
          alt="Udir logo"
          width={200}
          height={150}
          data-html2canvas-ignore="true"
        />
      </div>

      <div className="flex items-center">
        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center gap-4">
          <Button 
            variant="ghost" 
            size="sm"
            onClick={handleLanguageChange}
            className="flex items-center gap-2 hover:text-primary"
          >
            <IoLanguageOutline 
              className="text-lg" 
              role="img" 
              aria-label={ChangeLanguage} 
            />
            <span>{ChangeLanguage}</span>
          </Button>
          
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleLogout}
            className="flex items-center gap-2 hover:bg-neutral hover:text-white hover:border-transparent"
          >
            <IoLogOutOutline 
              className="text-xl" 
              role="img" 
              aria-label={Logout} 
            />
            <span>{Logout}</span>
          </Button>
        </div>

        {/* Mobile Navigation */}
        <div className="flex md:hidden">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="icon"
                className="hover:bg-base-200"
              >
                <FaBars 
                  className="text-xl" 
                  role="img" 
                  aria-label="Meny ikon" 
                />
              </Button>
            </DropdownMenuTrigger>
            
            <DropdownMenuContent 
              className="w-52 bg-background shadow-lg" 
              align="end"
            >
              <DropdownMenuItem 
                onClick={handleLanguageChange} 
                className="cursor-pointer hover:bg-muted"
              >
                <div className="flex items-center gap-3">
                  <IoLanguageOutline
                    className="text-lg"
                    role="img"
                    aria-label={ChangeLanguage}
                  />
                  <span>{ChangeLanguage}</span>
                </div>
              </DropdownMenuItem>
              
              <DropdownMenuItem
                onClick={handleLogout}
                className="cursor-pointer hover:bg-muted"
              >
                <div className="flex items-center gap-3">
                  <IoLogOutOutline 
                    className="text-xl" 
                    role="img" 
                    aria-label={Logout} 
                  />
                  <span>{Logout}</span>
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}

export default NavBar;