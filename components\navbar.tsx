"use client";

import React from "react";
import { FaBars } from "react-icons/fa";
import { IoLanguageOutline, IoLogOutOutline } from "react-icons/io5";
import Image from "next/image";
import { createSharedPathnamesNavigation } from "next-intl/navigation";
import { locales } from "@/middleware";
import { federatedlogout } from "@/app/lib/federatedLogout";
import UdirLogoImg from "@/app/assets/udir-logo.png";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const { useRouter, usePathname } = createSharedPathnamesNavigation({ locales });

function NavBar({ ChangeLanguage, Logout, locale, language }: any) {
  const router = useRouter();
  const pathname = usePathname();

  const handleLanguageChange = async () => {
    const newLocale = locale === "nb" ? "nn" : "nb";
    
    // Set the new locale in cookie
    await fetch(`/api/setDefaultLanguage?defaultLocale=${newLocale}`);
    
    // Navigate to the new locale
    router.replace(pathname, { locale: newLocale });

    // Refresh data from server and re-render server components
    router.refresh();
  };

  return (
    <div className="flex items-center justify-between w-full px-4 py-2 bg-base-100">
      <div className="flex-shrink-0">
        <Image
          src={UdirLogoImg}
          alt="Udir logo"
          width={200}
          height={150}
          data-html2canvas-ignore="true"
        />
      </div>

      <div className="flex items-center">
        <div className="hidden md:flex items-center gap-4">
          <button onClick={handleLanguageChange}>
            <div className="flex gap-1 items-center hover:text-primary cursor-pointer">
              <span className="text-lg">
                <IoLanguageOutline role="img" aria-label={ChangeLanguage} />
              </span>
              <span>{ChangeLanguage}</span>
            </div>
          </button>
          <button
            className="inline-flex items-center justify-center whitespace-nowrap rounded-[3px] text-base font-medium ring-offset-background transition-colors focus-visible:outline focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-neutral hover:text-white hover:border-transparent normal-case h-10 px-4 py-2"
            onClick={async () => {
              sessionStorage.removeItem("pgs-locale-set");
              sessionStorage.removeItem("pgs-authenticated");
              sessionStorage.removeItem("rejectedFiles");

              await federatedlogout();
            }}
          >
            <span className="text-xl">
              <IoLogOutOutline role="img" aria-label={Logout} />
            </span>
            {Logout}
          </button>
        </div>
        <div className="flex md:hidden">
          <DropdownMenu>
            <DropdownMenuTrigger className="m-1 inline-flex items-center justify-center whitespace-nowrap rounded-[3px] text-xl font-medium ring-offset-background transition-colors focus-visible:outline focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-base-100 hover:text-foreground disabled:text-[#575757] h-10 w-10">
              <FaBars role="img" aria-label="Meny ikon" />
            </DropdownMenuTrigger>
            <DropdownMenuContent className="p-2 shadow rounded w-52 bg-stone-100 z-[1]">
              <DropdownMenuItem onClick={handleLanguageChange} className="cursor-pointer">
                <div className="flex gap-3 items-center">
                  <span className="text-lg">
                    <IoLanguageOutline
                      role="img"
                      aria-label={ChangeLanguage}
                    />
                  </span>
                  <span>{ChangeLanguage}</span>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="flex gap-3 items-center cursor-pointer"
                onClick={async () => {
                  sessionStorage.removeItem("pgs-locale-set");
                    sessionStorage.removeItem("pgs-authenticated");
                    sessionStorage.removeItem("rejectedFiles");

                    await federatedlogout();
                  }}
                >
                  <span className="text-xl">
                    <IoLogOutOutline role="img" aria-label={Logout} />
                  </span>
                  <span>Logg ut</span>
                </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}

export default NavBar;
