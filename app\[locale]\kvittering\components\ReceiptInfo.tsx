import { getStatusInfoFromPgsa } from "@/app/lib/getStatusInfoFromPgsa";
import { getTranslations } from "next-intl/server";
import { getUserSessionData } from "@/app/lib/getUserSessionData";
import { IUserSessionData } from "@/app/interfaces/IUserSessionData";
import { FaCheck } from "react-icons/fa6";
import { FiInfo } from "react-icons/fi";
import { StatusInfoEmptyError } from "@/app/lib/exceptionTypes";

const ReceiptInfo = async () => {
  const userSessionData: IUserSessionData = await getUserSessionData();

  const [statusInfo, t] = await Promise.all([
    getStatusInfoFromPgsa(userSessionData.userId),
    getTranslations("Kvittering"),
  ]);

  if (!statusInfo) throw new StatusInfoEmptyError();

  return (
    <>
      <div  className="flex flex-col gap-6">
        <div className="lg:w-2/3 gap-8">
          <div className="card rounded-none w-full bg-white">
            <div className="card-body">
              <div
                className="alert bg-gray-100 border-primary border-2 rounded-none"
                data-html2canvas-ignore="true"
              >
                <span className="text-xl">
                  <FiInfo role="img" aria-label="Info ikon" />
                </span>

                <span className="text-infoTxt">{t("DoNotClosePage")}</span>
              </div>

              <div className="sm:grid grid-cols-[130px_auto] gap-3 ">
                <div className="font-semibold">{t("CandidateNumber")}</div>
                <div className="">{userSessionData.candidateNumber}</div>

                <div className="font-semibold">{t("Fag")}</div>
                <div className="">
                  {statusInfo.SubjectCode} - {statusInfo.SubjectName}
                </div>

                <div className="font-semibold">{t("Status")}</div>
                <div className="flex gap-2 flex-row">
                  <FaCheck
                    role="img"
                    size={22}
                    aria-label="Hake"
                    data-html2canvas-ignore="true"
                  />
                  {t("Submitted")}
                </div>
              </div>
              <div className="mt-6">{t("ExamAnswersAvailable")}</div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ReceiptInfo;
