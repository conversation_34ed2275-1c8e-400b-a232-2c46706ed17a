import { getTranslations } from "next-intl/server";
import { IUserSessionData } from "@/app/interfaces/IUserSessionData";
import { FaCheck } from "react-icons/fa6";
import { FiInfo } from "react-icons/fi";
import { IStatusInfo } from "@/app/interfaces/IStatusInfo";

interface ReceiptInfoProps {
  statusInfo: IStatusInfo | null;
  userSessionData: IUserSessionData;
}

const ReceiptInfo = async ({
  statusInfo,
  userSessionData,
}: ReceiptInfoProps) => {
  const t = await getTranslations("Kvittering");

  if (!statusInfo) return null; // Or handle the error appropriately

  return (
    <>
      <div className="flex flex-col gap-6">
        <div className="lg:w-2/3 gap-8">
          <div className="card rounded-none w-full bg-white">
            <div className="card-body">
              <div className="alert bg-gray-100 border-primary border-2 rounded-none">
                <span className="text-xl">
                  <FiInfo role="img" aria-label="Info ikon" />
                </span>

                <span className="text-infoTxt">{t("DoNotClosePage")}</span>
              </div>

              <div className="sm:grid grid-cols-[130px_auto] gap-3 ">
                <div className="font-semibold">{t("CandidateNumber")}</div>
                <div className="">{userSessionData.candidateNumber}</div>

                <div className="font-semibold">{t("Fag")}</div>
                <div className="">
                  {statusInfo.SubjectCode} - {statusInfo.SubjectName}
                </div>

                <div className="font-semibold">{t("Status")}</div>
                <div className="flex gap-2 flex-row">
                  <FaCheck role="img" size={22} aria-label="Hake" />
                  {t("Submitted")}
                </div>
              </div>
              <div className="mt-6">{t("ExamAnswersAvailable")}</div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ReceiptInfo;
