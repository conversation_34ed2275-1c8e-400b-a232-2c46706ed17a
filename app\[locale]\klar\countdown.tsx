"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { RouteEnum } from "@/app/enums/RouteEnum";
import { Skeleton } from "@/components/ui/skeleton";
import dayjs from "dayjs";

interface CountDownProps {
  teststartTime: string;
  serverTime: string;
  examStartsLabel: string;
  sendForwardLabel: string;
  daysLabel: string;
  dayLabel: string;
  hoursLabel: string;
  hourLabel: string;
  minutesLabel: string;
  minuteLabel: string;
  feilLabel: string;
  oneMinuteLabel: string;
  secondsLabel: string;
  secondLabel: string;
  examParts: string[];
  twoPartsLabel: string;
}
export default function Countdown({
  teststartTime,
  serverTime,
  examStartsLabel,
  twoPartsLabel,
  sendForwardLabel,
  daysLabel,
  dayLabel,
  hoursLabel,
  hourLabel,
  minutesLabel,
  minuteLabel,
  feilLabel,
  oneMinuteLabel,
  secondsLabel,
  secondLabel,
  examParts,
}: CountDownProps) {
  const [error, setError] = useState(false);
  const [loading, setLoading] = useState(true);

  const [seconds, setSeconds] = useState(0);
  const [days, setDays] = useState(0);
  const [hours, setHours] = useState(0);
  const [minutes, setMinutes] = useState(0);
  const router = useRouter();
  const [now, setNow] = useState<dayjs.Dayjs | null>(null);

  // Initialize current time on client-side only
  useEffect(() => {
    setNow(dayjs());
  }, []);

  useEffect(() => {
    if (teststartTime !== null && now !== null) {
      const diff = dayjs(serverTime).diff(now);

      const interval = setInterval(() => {
        const timeDiff = dayjs(teststartTime).diff(
          dayjs().add(diff, "millisecond")
        );

        const daysValue = Math.floor(timeDiff / (1000 * 3600 * 24));
        setDays(daysValue);
        const hoursValue = Math.floor(
          (timeDiff % (3600 * 24 * 1000)) / (1000 * 3600)
        );
        setHours(hoursValue);

        const minutesValue = Math.floor(
          (timeDiff % (3600 * 1000)) / (1000 * 60)
        );
        setMinutes(minutesValue);

        const remainingSeconds = Math.floor((timeDiff % (1000 * 60)) / 1000);
        setSeconds(remainingSeconds);
        setLoading(false);
        if (timeDiff <= 0) {
          clearInterval(interval);
          router.push(RouteEnum.Eksamensoppgave);
        }
      }, 1000);
      return () => clearInterval(interval);
    } else {
      console.log(error);

      setError(true);
    }
  }, [teststartTime, serverTime, now, router, error]);

  return (
    <div>
      {error ? (
        <span>{feilLabel}</span>
      ) : (
        <div>
          {examParts.length > 1 ? (
            <p className="">{twoPartsLabel}</p>
          ) : (
            <p className=""> {examStartsLabel} </p>
          )}

          <div className="flex gap-5 justify-center mt-5">
            {loading ? (
              <div className="flex items-center gap-4">
                <Skeleton className=" w-12 h-8 rounded-md bg-base-200" />
                <Skeleton className=" w-12 h-8 rounded-md bg-base-200" />
                <Skeleton className=" w-12 h-8 rounded-md bg-base-200" />
              </div>
            ) : (
              <div>
                {days === 0 && hours === 0 && minutes < 1 && seconds > 0 ? (
                  <div className="text-2xl">{oneMinuteLabel}</div>
                ) : (
                  <div className="flex items-center gap-4">
                    <div hidden={days === 0}>
                      <span className="countdown text-3xl">
                        <span
                          style={
                            { "--value": days || 0 } as React.CSSProperties
                          }
                        ></span>
                      </span>
                      {days === 1 ? (
                        <span> {dayLabel}</span>
                      ) : (
                        <span> {daysLabel}</span>
                      )}
                    </div>
                    <div
                      hidden={days === 0 && hours === 0}
                      className="flex items-baseline gap-1"
                    >
                      <span className="countdown text-3xl">
                        <span
                          style={
                            { "--value": hours || 0 } as React.CSSProperties
                          }
                        ></span>
                      </span>
                      {hours === 1 ? (
                        <span> {hourLabel}</span>
                      ) : (
                        <span> {hoursLabel}</span>
                      )}
                    </div>
                    <div
                      hidden={days === 0 && hours === 0 && minutes < 1}
                      className="flex items-baseline gap-1"
                    >
                      <span className="countdown text-3xl">
                        <span
                          style={
                            { "--value": minutes || 0 } as React.CSSProperties
                          }
                        ></span>
                      </span>
                      {minutes < 2 ? (
                        <span> {minuteLabel}</span>
                      ) : (
                        <span> {minutesLabel}</span>
                      )}
                    </div>
                    <div
                      hidden={days === 0 && hours === 0 && minutes < 1}
                      className="flex items-baseline gap-1"
                    >
                      <span className="countdown text-3xl">
                        <span
                          style={
                            { "--value": seconds || 0 } as React.CSSProperties
                          }
                        >
                          {seconds}
                        </span>
                      </span>
                      {secondsLabel}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
          <div className="mt-5">{sendForwardLabel}</div>
        </div>
      )}
    </div>
  );
}
