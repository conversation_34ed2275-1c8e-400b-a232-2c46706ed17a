//import { Trans, useTranslation } from "react-i18next";
import DayPassword from "../../components/dayPassword";
import { Metadata } from "next";
import GotAccess from "../../components/gotAccess";
import { getUserSessionData } from "../lib/getUserSessionData";
import { getTranslations } from "next-intl/server";
import { IUserSessionData } from "../interfaces/IUserSessionData";
import Image from "next/image";
import { headers } from "next/headers";
import PersonStandingImg from "@/app/assets/PersonStanding.png";

export const metadata: Metadata = {
  title: "PGS - Autoriser",
  description: "Autoriser bruker",
};

export default async function NeedAccess() {
  let userSessionData: IUserSessionData, t;
  const header = await headers();

  try {
    userSessionData = await getUserSessionData();
     t = await getTranslations("Autorisering");
  } catch (error) {
    throw new Error(
      `Feil under henting av brukerdata på Hjem siden: ${error}`
    );
  }

  return (
    <div className="flex flex-col gap-6 mb-8">
      <h1 className="text-5xl font-bold">
        {t("WelcomeMessage", { displayName: userSessionData?.name })}
      </h1>

      <div className="">
        <div className="flex flex-col lg:flex-row gap-8">
          <div className="">
            <GotAccess
              alreadyAccessLabel={t("AlreadyAccess")}
              needAccessLabel={t("NeedAccessToExam")}
              gotAccessLabel={t("GotAccess")}
              noAccessLabel={t("NoAccessError")}
              controlCodeLabel={t("ControlCode")}
              messageSentLabel={t("MessageSent")}
              dontRefreshLabel={t("DontRefresh")}
              accessDeniedLabel={t("AccessDenied")}
              accessDeniedMessageLabel={t("AccessDeniedMessage")}
              sendNewRequestLabel={t("SendNewRequest")}
              sessionId={userSessionData.userSessionId}
            />
            <DayPassword
              sendLabel={t("Send")}
              writePasswordLinkLabel={t("ExamInviligatorWritePassword")}
              writeDayPasswordLabel={t("WriteDayPassword")}
              fieldEmptyLabel={t("FieldEmpty")}
              wrongPasswordLabel={t("WrongPassword")}
            />
            
          </div>

          <div className="flex justify-center">
            <Image
              src={PersonStandingImg}
              width={500}
              height={500}
              alt={t("PictureStandingPerson")}
              priority={false}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
