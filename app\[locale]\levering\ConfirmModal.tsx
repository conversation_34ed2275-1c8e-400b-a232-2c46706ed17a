import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

interface ConfirmDeleteModalProps {
  isOpen: boolean;
  fileName: string;
  onClose: () => void;
  onConfirm: () => void;
  confirmDeleteHeading: string;
  confirmDeleteMessage: string;
  confirmDeleteConfirmBtnLabel: string;
  cancelLabel: string;
}

function ConfirmDeleteModal({
  isOpen,
  onClose,
  onConfirm,
  fileName,
  confirmDeleteHeading,
  confirmDeleteMessage,
  confirmDeleteConfirmBtnLabel,
  cancelLabel: cancelLabel,
}: ConfirmDeleteModalProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="rounded">
        <AlertDialogHeader>
          <AlertDialogTitle className="font-bold text-lg">
            {confirmDeleteHeading}
          </AlertDialogTitle>
          <AlertDialogDescription className="py-4 break-words">
            <span>{confirmDeleteMessage}:</span>
            <span className="font-semibold ml-2">{fileName}</span> ?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogAction
            onClick={onConfirm}
            className="bg-primary text-white hover:bg-primary/90"
          >
            {confirmDeleteConfirmBtnLabel}
          </AlertDialogAction>
          <AlertDialogCancel onClick={onClose}>
            {cancelLabel}
          </AlertDialogCancel>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

export default ConfirmDeleteModal;
