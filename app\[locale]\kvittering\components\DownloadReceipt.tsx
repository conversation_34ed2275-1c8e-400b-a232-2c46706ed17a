import { getTranslations } from "next-intl/server";
import DownloadButton from "./DownloadButton";

interface DownloadReceiptProps {
  receiptInfo: any;
  submittedFiles: any;
}

const DownloadReceipt = async ({ receiptInfo, submittedFiles }: DownloadReceiptProps) => {
  const t = await getTranslations("Kvittering");

  return <DownloadButton downloadRecieptLabel={t("DownloadReciept")} receiptInfo={receiptInfo} submittedFiles={submittedFiles} />;
};

export default DownloadReceipt;
