import { IUserSessionData } from "@/app/interfaces/IUserSessionData";
import { initializeCandidate } from "@/app/lib/initializeCandidate";
import {
  getPgsaStatusInfoFromRedis,
  getUserInfoFromRedis,
  saveUserInfoToRedis,
} from "@/app/lib/redisHelper";
import { ValidLocale } from "@/app/interfaces/ILocale";
import { NextAuthOptions, Profile, User } from "next-auth";
import DuendeIDS6Provider from "next-auth/providers/duende-identity-server6";
import { cookies, headers } from "next/headers";
import { v4 as uuidv4 } from "uuid";
import jwt from "jsonwebtoken";
import { getAppInsightsServer } from "@/app/lib/appInsightsServer";
import { CandidateStatusEnum } from "@/app/enums/CandidateStatusEnum";
import { updateCandidateStatus } from "@/app/lib/updateCandidateStatus";
import AuditLogService from "@/db/services/auditLogService";
import { OperationEnum } from "@/app/enums/OperationEnum";
import { getClientIp } from "@/app/lib/getClientIp";
import { IAuditLogData } from "@/app/interfaces/IAuditLogData";
import { IStatusInfo } from "@/app/interfaces/IStatusInfo";
import { RouteEnum } from "@/app/enums/RouteEnum";
import dayjs from "dayjs";
import { getRoute } from "@/app/lib/getRoute";
import { sendAuditLogToServiceBus } from "@/app/lib/serviceBusClient";

const COOKIE_SECRET = process.env.PGS_USER_COOKIE_SECRET || "";
const COOKIE_NAME = process.env.PGS_USER_COOKIE_NAME || "";
const CLIENT_ID = process.env.NEXTAUTH_UIDP_CLIENT_ID || "";
const CLIENT_SECRET = process.env.NEXTAUTH_UIDP_CLIENT_SECRET || "";

const telemetryClient = getAppInsightsServer();

export interface ExtendedSession {
  user: {
    idToken?: string;
    accessToken?: string;
    userInfo?: User;
    expiresAt: number;
  } & User;
}

interface ExtendedUser extends User {
  userId?: string;
  candidateNumber?: string;
}

interface ExtendedProfile extends Profile {
  role?: string;
}

function numberToGuid(number: number) {
  let hexString = number.toString(16);

  if (hexString.length > 12) {
    hexString = hexString.slice(-12);
  } else {
    hexString = hexString.padStart(12, "0");
  }

  let guid = `00000000-0000-0000-0000-${hexString}`;

  return guid;
}

export const authOptions: NextAuthOptions = {
  providers: [
    DuendeIDS6Provider({
      clientId: CLIENT_ID,
      clientSecret: CLIENT_SECRET,
      //  wellKnown: `${process.env.NEXTAUTH_UIDP_URL}/.well-known/openid-configuration`,
      issuer: process.env.NEXTAUTH_UIDP_URL,
      checks: ["pkce", "state"],
      id: "UIDP",
      name: "UIDP",
      authorization: {
        params: {
          scope: process.env.NEXTAUTH_UIDP_CLIENT_SCOPE,
          acr_values: "udir:kandidat",
        },
      },
      idToken: true,
      userinfo: {
        async request(context) {
          return await context.client.userinfo(
            context.tokens.access_token ?? ""
          );
        },
      },
      async profile(profile) {
        const malform = profile["udir:pas2:målform"] as string;
        const language: ValidLocale =
          malform?.toLowerCase() === "nb-no" ? "nb" : "nn";

        return {
          id: profile.sub,
          name: `${profile.given_name} ${profile.family_name}`,
          uid: profile.uid,
          language,
          userId: numberToGuid(
            profile["kandidat_pameldingid"]
              ? profile["kandidat_pameldingid"]
              : 0
          ),
          //candidateNumber: profile["udir:pas2:kandidatnr"],
          candidateNumber: profile["kandidat_username"],
          candidateGroupCode: profile["udir:pas2:kandidatgruppe.kode"],
          dayCode: profile["udir:pas2:eksamen.dagskode"],
        };
      },
    }),
  ],
  pages: {
    signIn: "/auth/signin", // Tilpasset rute for innlogging
    error: "/auth/error",
  },
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: "jwt",
    maxAge: 8 * 60 * 60, // 8 timer
  },
  jwt: {},

  callbacks: {
    async signIn({ user, profile }) {
      const pgsProfile = profile as ExtendedProfile;

      if (pgsProfile && pgsProfile.role?.includes("urn:udir:pgsa:kandidat")) {
        // Sett validation cookie ved vellykket innlogging
        (await cookies()).set(COOKIE_NAME, jwt.sign(pgsProfile.role, COOKIE_SECRET), {
          httpOnly: true,
          secure: process.env.PGS_ENVIRONMENT !== "localhost",
          sameSite: "lax",
          path: "/",
        });
        return true;
      }

      return false;
    },

    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      // Allow callbacks to identity server Server
      else if (new URL(url).origin === process.env.NEXTAUTH_UIDP_URL)
        return url;
      return baseUrl;
    },
    async jwt({ token, user, account }) {
      if (user) {
        const { dayCode, ...restUser } = user as IUserSessionData;
        const userSessionId = uuidv4();

        token.user = {
          ...restUser,
          userSessionId: userSessionId,
        };
        token.idToken = account?.id_token;

        try {
          const userId = (user as ExtendedUser).userId;
          if (!userId)
            throw new Error("Autentiseringsfeil: userId er ikke tilgjengelig");

          var logActivityService = AuditLogService.getInstance();

          const [saveUserResult, pgsaInfoFromRedis] = await Promise.all([
            saveUserInfoToRedis({
              ...user,
              userSessionId: userSessionId,
              isAuthorized: false,
              isNewAuthentication: true,
            } as IUserSessionData),
            getPgsaStatusInfoFromRedis(userId),
          ]);

          let finalStatus: IStatusInfo | null = null;

          if (!pgsaInfoFromRedis) {
            finalStatus = await initializeCandidate({
              ...user,
              userSessionId: userSessionId,
              isAuthorized: false,
            } as IUserSessionData);
          } else if (
            pgsaInfoFromRedis.Status === CandidateStatusEnum.IkkeInnlogget
          ) {
            await updateCandidateStatus(
              CandidateStatusEnum.IkkeInnlogget,
              CandidateStatusEnum.VenterPaaDagspassordIkkeAutentisert,
              pgsaInfoFromRedis.TestPartId,
              pgsaInfoFromRedis.CandidateNumber,
              pgsaInfoFromRedis.ExamGroupCode,
              pgsaInfoFromRedis.CandidateNumber,
              pgsaInfoFromRedis.UserId,
              ""
            );
          }

          finalStatus = finalStatus || pgsaInfoFromRedis;

          const baseData = {
            kandidatpaameldingId: userId,
            rolle: "Kandidat",
            kandidatNr: (token.user as IUserSessionData).candidateNumber,
            kandidatFornavn: (token.user as IUserSessionData).name,
            kandidatEtternavn: (token.user as IUserSessionData).name,
            sesjonsId: (token.user as IUserSessionData).userSessionId,
            ip: await getClientIp(),
            eksamensdel: "",
            filnavn: "",
          };

          let userInfoFromRedis = null;
          try {
            userInfoFromRedis = await getUserInfoFromRedis(
              userSessionId,
              (token.user as IUserSessionData).candidateNumber
            );
          } catch (error) {
            console.error("Error getting user info from Redis:", error);
          }

          const data = await logActivityService.buildAuditLogData(
            baseData,
            OperationEnum.InnloggingNySesjon,
            {
              side: finalStatus
                ? getRoute(
                    finalStatus,
                    userInfoFromRedis ?? ({} as IUserSessionData)
                  )
                : "",
            }
          );

          if (!data) {
            console.error("Failed to build audit log data for login");
            return token;
          }

          try {
            const headersList = await headers();
            const userAgent =
              headersList.get("user-agent") || "Unknown User Agent";
            await sendAuditLogToServiceBus(data, userAgent);
          } catch (error: any) {
            console.error("Error sending message to Service Bus:", error);
          }
        } catch (error) {
          telemetryClient?.trackException({
            exception: error as Error,
            properties: {
              component: "NextAuthConfig",
              action: "jwt",
              userId: (user as ExtendedUser).userId,
            },
          });
          console.error(error);
        }
      }

      return token;
    },
    async session({ session, token }) {
      session.user = {
        // append the id token to the next-auth session
        idToken: token.idToken,
        userInfo: token.user,
      } as any;

      return session;
    },
  },
  events: {},
  debug: false,
  theme: {
    brandColor: "red",
    colorScheme: "light",
  },
};
