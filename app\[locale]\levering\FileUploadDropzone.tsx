"use client";

import React, { useCallback, useEffect, useState } from "react";
import { useDropzone, FileRejection, DropEvent } from "react-dropzone";
import { FiPlus, FiUpload } from "react-icons/fi";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { useUploadedFiles } from "./UploadedFilesContext";
import { v4 as uuidv4 } from "uuid";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { logMetric } from "@/app/lib/appInsightsClient";

interface FileUploadDropzoneProps {
  candidateNumber: string;
  examGroupCode: string;
  subjectCode: string;
  examCodesWithCustomFilesizes: ISubjectCodeFileSizes[];
  tooManyFilesTitle: string;
  tooManyFilesMessage: string;
  uploadInProgress: string;
  dragFilesHere: string;
  uploadLoadYourFiles: string;
  uploadFileLabel: string;
  allowedMimeTypes: IAllowedMimeTypes[];
  orLabel: string;
  testPartId: number;
}

const FileUploadDropzone: React.FC<FileUploadDropzoneProps> = ({
  candidateNumber,
  examCodesWithCustomFilesizes,
  examGroupCode,
  dragFilesHere,
  uploadLoadYourFiles,
  uploadFileLabel,
  allowedMimeTypes,
  orLabel,
  subjectCode,
  tooManyFilesTitle,
  tooManyFilesMessage,
  testPartId,
}) => {
  const { uploadedFiles, addFile, isUploading, uploadStatus, setUploadStatus } =
    useUploadedFiles();
  const { toast } = useToast();
  const [filesizeLimit, setFilesizeLimit] = useState<number>(40);

  useEffect(() => {
    const limit =
      examCodesWithCustomFilesizes.find((item) => item.FagKode === subjectCode)
        ?.UploadFileSizeMegaBytes ?? 40;
    setFilesizeLimit(limit);
  }, [examCodesWithCustomFilesizes, subjectCode]);

  const onDrop = useCallback(
    async (
      acceptedFiles: File[],
      fileRejections: FileRejection[],
      event: DropEvent
    ) => {
      if (isUploading) return; // Prevent drop when uploading

      if (
        uploadedFiles.length + acceptedFiles.length + fileRejections.length >
        20
      ) {
        return toast({
          variant: "destructive",
          title: tooManyFilesTitle,
          description: tooManyFilesMessage,
          duration: 8000,
        });
      }

      const newFiles = acceptedFiles.map((file) => {
        const fileExtension = `.${file.name.split(".").pop()}`;

        const commonProperties = {
          file: file,
          fileName: file.name,
          fileGuid: uuidv4(),
          size: file.size,
          uploadDate: new Date(),
          checked: false,
          mimetype: file.type,
          fileExtension: fileExtension,
          isRejected: false,
          error: "",
          testPartId: testPartId,
          uploadFinished: false,
          downloading: false,
          deleting: false,
        };

        if (uploadedFiles.some((f) => f.fileName === file.name)) {
          return {
            ...commonProperties,
            isRejected: true,
            error: "file-conflicting-name",
            uploadFinished: true,
          };
        } else if (
          !allowedMimeTypes.some(
            (type) =>
              type.FileExtension.toLowerCase() === fileExtension.toLowerCase()
          )
        ) {
          return {
            ...commonProperties,
            isRejected: true,
            error: "file-invalid-type",
            uploadFinished: true,
          };
        } else if (file.size === 0) {
          return {
            ...commonProperties,
            isRejected: true,
            error: "file-empty",
            uploadFinished: true,
          };
        } else {
          return commonProperties;
        }
      });

      const rejectedFiles = fileRejections.map(({ file, errors }) => ({
        file: file,
        fileName: file.name,
        size: file.size,
        fileGuid: uuidv4(),
        uploadDate: new Date(),
        uploadFinished: true,
        error: errors[0]?.code ?? "",
        isRejected: true,
        checked: false,
        mimetype: file.type,
        fileExtension: `.${file.name.split(".").pop()}`,
        testPartId: testPartId,
        downloading: false,
        deleting: false,
      }));

      const filesToUpload = newFiles.filter((file) => !file.isRejected);
      setUploadStatus({ currentFile: 0, totalFiles: filesToUpload.length });

      // Add all files to state, including rejected ones
      for (const file of [...newFiles, ...rejectedFiles]) {
        await addFile(file, candidateNumber, examGroupCode);
      }

      const uploadedFilesTable = document.getElementById("uploadedFilesTable");
      if (uploadedFilesTable) {
        uploadedFilesTable.scrollIntoView({ behavior: "smooth" });
      }

      logMetric(
        "pgskUploadBatch",
        filesToUpload.length + rejectedFiles.length,
        {
          filesUploaded: filesToUpload.length,
          filesRejected: rejectedFiles.length,
          totalFileSizeInBatch: filesToUpload.reduce(
            (acc, file) => acc + file.size,
            0
          ),
          numberOfFilesInBatch: filesToUpload.length + rejectedFiles.length,
        }
      );
    },
    [
      uploadedFiles,
      addFile,
      toast,
      allowedMimeTypes,
      tooManyFilesTitle,
      tooManyFilesMessage,
      candidateNumber,
      examGroupCode,
      setUploadStatus,
      isUploading,
      testPartId,
    ]
  );

  const { getRootProps, getInputProps, open, isDragActive } = useDropzone({
    onDrop,
    maxSize: filesizeLimit * 1024 * 1024,
    disabled: isUploading,
  });

  const dropzoneColors = isDragActive
    ? "bg-gray-200 border-solid"
    : "bg-gray-100 border-dashed";

  return (
    <div className="flex flex-col w-full md:w-3/5">
      <div
        {...getRootProps({
          className: `${
            isUploading ? "cursor-not-allowed" : "cursor-pointer"
          } h-48 drop-shadow-md flex flex-col gap-4 items-center justify-center border-2 border-primary transition duration-300 ${dropzoneColors} ${
            isUploading ? "opacity-90 cursor-not-allowed" : ""
          }`,
          role: "region",
          "aria-label": uploadLoadYourFiles,
        })}
      >
        <input {...getInputProps()} disabled={isUploading} />
        <div className="btn btn-ghost text-5xl">
          {isUploading ? (
            <AiOutlineLoading3Quarters
              className="animate-spin text-4xl"
              role="img"
              aria-label="laster opp"
            />
          ) : isDragActive ? (
            <FiUpload role="img" aria-label="filopplastingsikon" />
          ) : (
            <FiPlus role="img" aria-label="pluss" />
          )}
        </div>

        <div className="text-center">
          {isUploading ? (
            <>
              <div>
                Laster opp {uploadStatus.currentFile} av{" "}
                {uploadStatus.totalFiles} filer
              </div>
            </>
          ) : (
            dragFilesHere
          )}
        </div>
      </div>
      <div className="divider">{orLabel}</div>
      <div>
        <Button
          variant="outline"
          type="button"
          onClick={open}
          disabled={isUploading}
          className={`flex items-center bg-transparent border-black gap-1 w-full h-12 normal-case text-sm md:text-base lg:text-lg ${
            isUploading ? "cursor-not-allowed" : ""
          }`}
        >
          <span className="text-lg hidden sm:block">
            <FiUpload role="img" aria-label="last opp fil" />
          </span>
          {uploadFileLabel}
        </Button>
      </div>
    </div>
  );
};

export default FileUploadDropzone;
