"use client";

import { BiArrowToBottom } from "react-icons/bi";
import { PDFDownloadLink } from "@react-pdf/renderer";
import ReceiptDocument from "./ReceiptDocument";
import { logActivity } from "@/app/lib/logActivity";
import { OperationEnum } from "@/app/enums/OperationEnum";
import { Button } from "@/components/ui/button";

interface Props {
  downloadRecieptLabel: string;
  receiptInfo: any; // Define a proper interface for this
  submittedFiles: any; // Define a proper interface for this
}

const DownloadButton = ({ downloadRecieptLabel, receiptInfo, submittedFiles }: Props) => {
  const handleDownload = async () => {
    await logActivity("", "", OperationEnum.KvitteringNedlastet);
  };

  return (
    <PDFDownloadLink
      document={<ReceiptDocument receiptInfo={receiptInfo} submittedFiles={submittedFiles} />}
      fileName="Kvittering.pdf"
    >
      {({ loading }) => (
        <Button
          variant="ghost"
          className="mb-10"
          onClick={handleDownload}
          disabled={loading}
        >
          <BiArrowToBottom size={22} role="img" aria-label="Nedlastingsikon" />
          <span>{loading ? "Laster inn dokument..." : downloadRecieptLabel}</span>
        </Button>
      )}
    </PDFDownloadLink>
  );
};

export default DownloadButton;
